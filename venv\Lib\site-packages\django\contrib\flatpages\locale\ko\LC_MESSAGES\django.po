# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, <PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2019
# Jun<PERSON><PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-07-04 14:22+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Korean (http://www.transifex.com/django/django/language/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Advanced options"
msgstr "고급 옵션"

msgid "Flat Pages"
msgstr "플랫 페이지"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr "앞, 뒤에 슬래시(/)를 넣으세요. 예:'/about/contact/' "

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"이 문자열은 문자, 숫자, 점, 언더스코어, 대쉬, 슬래시, 틸드만 사용해야 합니다."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "맨앞에 슬래시(/)를 반드시 넣으세요. 예: '/about/contact'"

msgid "URL is missing a leading slash."
msgstr "URL 맨 앞의 슬래시가 없습니다."

msgid "URL is missing a trailing slash."
msgstr "URL 맨 뒤의 슬래시가 없습니다."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "%(site)s 사이트에 %(url)s 을/를 사용한 플랫 페이지가 이미 존재합니다."

msgid "title"
msgstr "제목"

msgid "content"
msgstr "내용"

msgid "enable comments"
msgstr "사용 가능한 코멘트"

msgid "template name"
msgstr "템플릿 이름"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"예: 'flatpages/contact_page.html' 를 사용할 수 없는 경우 시스템이 'flatpages/"
"default.html' 을/를 사용합니다."

msgid "registration required"
msgstr "등록이 필요합니다"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "체크할 경우, 로그인한 사용자만 해당 페이지를 볼 수 있습니다."

msgid "sites"
msgstr "사이트"

msgid "flat page"
msgstr "플랫 페이지"

msgid "flat pages"
msgstr "플랫 페이지들"
