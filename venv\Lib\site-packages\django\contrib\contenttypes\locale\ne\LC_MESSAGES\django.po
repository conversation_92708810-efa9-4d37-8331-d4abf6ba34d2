# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Nepali (http://www.transifex.com/django/django/language/ne/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ne\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "कन्टेन्ट टाइपहरु"

msgid "python model class name"
msgstr "पाइथन मोडेल क्लासको नाम"

msgid "content type"
msgstr "कन्टेन्ट टाइप"

msgid "content types"
msgstr "कन्टेन्ट टाइपहरु"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "कन्टेन्ट टाइप %(ct_id)s वस्तु सँग सम्बन्धित मोडेल छैन ।"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "कन्टेन्ट टाइप %(ct_id)s वस्तु %(obj_id)s छैन ।"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s वस्तुमा get_absolute_url() तरिका छैन ।"
