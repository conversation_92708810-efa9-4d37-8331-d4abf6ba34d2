# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2011
# <AUTHOR> <EMAIL>, 2011,2013
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
# <AUTHOR> <EMAIL>, 2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2023-04-24 19:03+0000\n"
"Last-Translator: X Bello <<EMAIL>>, 2023\n"
"Language-Team: Galician (http://www.transifex.com/django/django/language/"
"gl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: gl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Opcións avanzadas"

msgid "Flat Pages"
msgstr "Páxinas simples"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr "Exemplo: “/about/contact”. Lembre incluír as barras inicial e final."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Este valor soamente pode conter letras, números, puntos, guións baixos, "
"guións, barras inclinadas e tiles do eñe (~)."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Exemplo: “/about/contact”. Lembre incluír a barra inicial."

msgid "URL is missing a leading slash."
msgstr "Falta unha barra inclinada no principio da URL."

msgid "URL is missing a trailing slash."
msgstr "Falta unha barra inclinada no final da URL."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Xa existe unha páxina simple con url %(url)s no sitio %(site)s"

msgid "title"
msgstr "título"

msgid "content"
msgstr "contido"

msgid "enable comments"
msgstr "activar comentarios"

msgid "template name"
msgstr "nome da plantilla"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Exemplo: “flatpages/contact_page.html”. Se non se especifica, o sistema "
"usará “flatpages/default.html”."

msgid "registration required"
msgstr "require rexistro"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Se se marca, só poderán ver a páxina os usuarios identificados."

msgid "sites"
msgstr "sitios"

msgid "flat page"
msgstr "páxina simple"

msgid "flat pages"
msgstr "páxinas simples"
