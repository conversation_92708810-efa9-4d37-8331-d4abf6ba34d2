# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014-2015
# <AUTHOR> <EMAIL>, 2019,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-04-07 14:40+0200\n"
"PO-Revision-Date: 2021-09-22 16:47+0000\n"
"Last-Translator: znotdead <<EMAIL>>\n"
"Language-Team: Belarusian (http://www.transifex.com/django/django/language/"
"be/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: be\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Humanize"
msgstr "Ачалавечваньне"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}ы"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}ы"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}ы"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}і"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}і"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}ы"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}ы"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}ы"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}ы"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}ы"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}ы"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s мільён"
msgstr[1] "%(value)s мільёны"
msgstr[2] "%(value)s мільёнаў"
msgstr[3] "%(value)s мільёнаў"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value).s мільярд"
msgstr[1] "%(value)s мільярды"
msgstr[2] "%(value)s мільярдаў"
msgstr[3] "%(value)s мільярдаў"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s трыльён"
msgstr[1] "%(value)s трыльёны"
msgstr[2] "%(value)s трыльёнаў"
msgstr[3] "%(value)s трыльёнаў"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s квадрыльён"
msgstr[1] "%(value)s квадрыльёны"
msgstr[2] "%(value)s квадрыльёнаў"
msgstr[3] "%(value)s квадрыльёнаў"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s квінтыльён"
msgstr[1] "%(value)s квінтыльёны"
msgstr[2] "%(value)s квінтыльёнаў"
msgstr[3] "%(value)s квінтыльёнаў"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s сэкстыльён"
msgstr[1] "%(value)s сэкстыльёны"
msgstr[2] "%(value)s сэкстыльёнаў"
msgstr[3] "%(value)s сэкстыльёнаў"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s сэптыльён"
msgstr[1] "%(value)s сэптыльёны"
msgstr[2] "%(value)s сэптыльёнаў"
msgstr[3] "%(value)s сэптыльёнаў"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s актыльён"
msgstr[1] "%(value)s актыльёны"
msgstr[2] "%(value)s актыльёнаў"
msgstr[3] "%(value)s актыльёнаў"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s нанільён"
msgstr[1] "%(value)s нанільёны"
msgstr[2] "%(value)s нанільёнаў"
msgstr[3] "%(value)s нанільёнаў"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s дэцыльён"
msgstr[1] "%(value)s дэцыльёны"
msgstr[2] "%(value)s дэцыльёнаў"
msgstr[3] "%(value)s дэцыльёнаў"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s ґуґал"
msgstr[1] "%(value)s ґуґлы"
msgstr[2] "%(value)s ґуґлаў"
msgstr[3] "%(value)s ґуґлаў"

msgid "one"
msgstr "адзін"

msgid "two"
msgstr "два"

msgid "three"
msgstr "тры"

msgid "four"
msgstr "чатыры"

msgid "five"
msgstr "пяць"

msgid "six"
msgstr "шэсьць"

msgid "seven"
msgstr "сем"

msgid "eight"
msgstr "восем"

msgid "nine"
msgstr "дзевяць"

msgid "today"
msgstr "сёньня"

msgid "tomorrow"
msgstr "заўтра"

msgid "yesterday"
msgstr "ўчора"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s таму"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "%(count)s гадзіну таму"
msgstr[1] "%(count)s гадзіны таму"
msgstr[2] "%(count)s гадзінаў таму"
msgstr[3] "%(count)s гадзінаў таму"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "%(count)s хвіліну таму"
msgstr[1] "%(count)s хвіліны таму"
msgstr[2] "%(count)s хвілінаў таму"
msgstr[3] "%(count)s хвілінаў таму"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "%(count)s сэкунду таму"
msgstr[1] "%(count)s сэкунды таму"
msgstr[2] "%(count)s сэкундаў таму"
msgstr[3] "%(count)s сэкундаў таму"

msgid "now"
msgstr "зараз"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "праз %(count)s сэкунду"
msgstr[1] "праз %(count)s сэкунды"
msgstr[2] "праз %(count)s сэкундаў"
msgstr[3] "праз %(count)s сэкундаў"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "праз %(count)s хвіліну"
msgstr[1] "праз %(count)s хвіліны"
msgstr[2] "праз %(count)s хвілінаў"
msgstr[3] "праз %(count)s хвілінаў"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "праз %(count)s гадзіну"
msgstr[1] "праз %(count)s гадзіны"
msgstr[2] "праз %(count)s гадзінаў"
msgstr[3] "праз %(count)s гадзінаў"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "праз %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d год"
msgstr[1] "%(num)d гадоў"
msgstr[2] "%(num)d гадоў"
msgstr[3] "%(num)d гадоў"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d месяц"
msgstr[1] "%(num)d месяцаў"
msgstr[2] "%(num)d месяцаў"
msgstr[3] "%(num)d месяцаў"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d тыдзень"
msgstr[1] "%(num)d тыдняў"
msgstr[2] "%(num)d тыдняў"
msgstr[3] "%(num)d тыдняў"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d дзень"
msgstr[1] "%(num)d дзён"
msgstr[2] "%(num)d дзён"
msgstr[3] "%(num)d дзён"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d гадзіна"
msgstr[1] "%(num)d гадзін"
msgstr[2] "%(num)d гадзін"
msgstr[3] "%(num)d гадзін"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d хвіліна"
msgstr[1] "%(num)d хвілін"
msgstr[2] "%(num)d хвілін"
msgstr[3] "%(num)d хвілін"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d год"
msgstr[1] "%(num)d гадоў"
msgstr[2] "%(num)d гадоў"
msgstr[3] "%(num)d гадоў"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d месяц"
msgstr[1] "%(num)d месяцаў"
msgstr[2] "%(num)d месяцаў"
msgstr[3] "%(num)d месяцаў"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d тыдзень"
msgstr[1] "%(num)d тыдняў"
msgstr[2] "%(num)d тыдняў"
msgstr[3] "%(num)d тыдняў"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d дзень"
msgstr[1] "%(num)d дзён"
msgstr[2] "%(num)d дзён"
msgstr[3] "%(num)d дзён"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d гадзіна"
msgstr[1] "%(num)d гадзін"
msgstr[2] "%(num)d гадзін"
msgstr[3] "%(num)d гадзін"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d хвіліна"
msgstr[1] "%(num)d хвілін"
msgstr[2] "%(num)d хвілін"
msgstr[3] "%(num)d хвілін"
