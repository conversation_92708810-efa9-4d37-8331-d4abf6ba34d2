from fastapi import FastAPI,Request,Form
from fastapi.responses import RedirectResponse
from fastapi.templating import Jinja2Templates
from tortoise.contrib.fastapi import register_tortoise
from model import Todo

app = FastAPI()
template = Jinja2Templates("pages")#html存放的位置

#数据库的绑定
register_tortoise(app,
                  db_url="mysql://root:58848484@localhost:3306/fastapi",
                  modules={"modules":[]},
                  add_exception_handlers=True,
                  generate_schemas=True)

todos = ["吃饭","睡觉","打豆豆"]


@app.get("/")
def index(request:Request, username=None):
    #从数据库获取todos的代码 
    # #ORM  获取说有的todos
    todos = Todo.all()
    return template.TemplateResponse("index.html",context={"request":request,"todos":todos})


@app.post("/todo")
def todo(todo=Form(None)):
    """处理用户发过来的todolist数据"""
    todos.insert(0,todo)
    #把新的数据存储到数据库中
    return RedirectResponse("/",status_code=302)