# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012-2015,2017,2020
# <PERSON><PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-05-17 12:29+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Hebrew (http://www.transifex.com/django/django/language/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % "
"1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

msgid "Administrative Documentation"
msgstr "תיעוד ניהולי"

msgid "Home"
msgstr "דף הבית"

msgid "Documentation"
msgstr "תיעוד"

msgid "Bookmarklets"
msgstr "ייסומניות"

msgid "Documentation bookmarklets"
msgstr "ייסומוניות תיעוד"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"כדי להתקין ייסומניות, יש לגרור את הקישור לסרגל הסימניות שלך, או קליק ימני "
"והוספה לסימניות. כעת ניתן לבחור את הייסומניה מכל עמוד באתר."

msgid "Documentation for this page"
msgstr "תיעוד לדף זה"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "מקפיץ אותך מכל עמוד לתיעוד התצוגה שייצרה אותו."

msgid "Tags"
msgstr "תגים"

msgid "List of all the template tags and their functions."
msgstr "רשימת כל ה-template tags והפונקציות שלהן."

msgid "Filters"
msgstr "מסננים"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr "פילטרים ופעולות אשר ניתן ליישם על משתנים בתבנית לשינוי הפלט."

msgid "Models"
msgstr "מודלים"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"מודלים הם תיאור כל האובייקטים במערכת והשדות הקשורים אליהם. לכל מודל יש רשימת "
"שדות אשר ניתן לגשת אליהם בתור משתני תבנית"

msgid "Views"
msgstr "Views"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"כל עמוד באתר הציבורי מיוצר ע\"י view. ה־view מגדיר את התבנית המשמשת ליצירת "
"העמוד ואת האובייקטים הזמינים לתבנית"

msgid "Tools for your browser to quickly access admin functionality."
msgstr "כלים המאפשרים לדפדפן שלך גישה מהירה ליכולות הניהול."

msgid "Please install docutils"
msgstr "נא להתקין את docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "נא לבקש ממנהל המערכת להתקין את <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "מודל: %(name)s"

msgid "Fields"
msgstr "שדות"

msgid "Field"
msgstr "שדה"

msgid "Type"
msgstr "סוג"

msgid "Description"
msgstr "תיאור"

msgid "Methods with arguments"
msgstr "מתודות עם פרמטרים"

msgid "Method"
msgstr "מתודות"

msgid "Arguments"
msgstr "פרמטרים"

msgid "Back to Model documentation"
msgstr "חזרה לתיעוד מודל"

msgid "Model documentation"
msgstr "תיעוד מודלים"

msgid "Model groups"
msgstr "קבוצות מודלים"

msgid "Templates"
msgstr "תבניות"

#, python-format
msgid "Template: %(name)s"
msgstr "תבנית: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "תבנית: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "נתיב חיפוש לתבנית <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(לא קיים)"

msgid "Back to Documentation"
msgstr "חזרה לתיעוד"

msgid "Template filters"
msgstr "פילטרים לתבניות"

msgid "Template filter documentation"
msgstr "תיעוד פילטר לתבנית"

msgid "Built-in filters"
msgstr "פילטרים מובנים"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"כדי להשתמש בפילטרים האלו יש להוסיף <code>%(code)s</code> בתבנית שלך לפני "
"השימוש בפילטר."

msgid "Template tags"
msgstr "תגים לתבניות"

msgid "Template tag documentation"
msgstr "תיעוד תגים לתבניות"

msgid "Built-in tags"
msgstr "תגים מובנים"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"כדי להשתמש בתגים האלו יש להוסיף <code>%(code)s</code> בתבנית שלך לפני השימוש "
"בתג."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "קונטקסט:"

msgid "Templates:"
msgstr "תבניות:"

msgid "Back to View documentation"
msgstr "חזרה לתיעוד ה-View"

msgid "View documentation"
msgstr "תיעוד View"

msgid "Jump to namespace"
msgstr "מעבר למרחב שמות"

msgid "Empty namespace"
msgstr "מרחב שמות ריק"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Views לפי מרחב שמות %(name)s"

msgid "Views by empty namespace"
msgstr "Views לפני מרחב שמות ריק"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    פונקציית View: <code>%(full_name)s</code>. שם: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "תג:"

msgid "filter:"
msgstr "סינון:"

msgid "view:"
msgstr "ה־view:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "יישום  %(app_label)r לא נמצא"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "מודל %(model_name)r לא נמצא ביישום %(app_label)r"

msgid "model:"
msgstr "מודל:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "עצמי `%(app_label)s.%(data_type)s` קשורים"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "עצמי `%(app_label)s.%(object_name)s` קשורים"

#, python-format
msgid "all %s"
msgstr "כל %s"

#, python-format
msgid "number of %s"
msgstr "מספר %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "לא נראה כי %s הוא עצם urlpattern"
