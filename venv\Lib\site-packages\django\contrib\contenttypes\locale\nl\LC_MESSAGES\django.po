# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-17 08:44+0000\n"
"Last-Translator: Tonnes <<EMAIL>>\n"
"Language-Team: Dutch (http://www.transifex.com/django/django/language/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Inhoudstypen"

msgid "python model class name"
msgstr "klass<PERSON><PERSON> van pythonmodel"

msgid "content type"
msgstr "inhoudstype"

msgid "content types"
msgstr "inhoudstypen"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Object van inhoudstype %(ct_id)s heeft geen bijbehorend model"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Object %(obj_id)s van inhoudstype %(ct_id)s bestaat niet"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s-objecten hebben geen get_absolute_url()-methode"
