# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2018-09-22 10:19+0000\n"
"Last-Translator: <PERSON> <mavrak<PERSON>.<EMAIL>>\n"
"Language-Team: Greek (http://www.transifex.com/django/django/language/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Humanize"
msgstr "Εξανθρώπιση"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}ο"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}ο"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}ο"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}ο"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}ο"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}ο"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}ο"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}ο"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}ο"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}ο"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}ο"

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f εκατομμύριο"
msgstr[1] "%(value).1f εκατομμύρια"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] " %(value)s  εκατομμύριο"
msgstr[1] " %(value)s  εκατομμύρια"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f δισεκατομμύριο"
msgstr[1] "%(value).1f δισεκατομμύρια"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s δισεκατομμύριο"
msgstr[1] "%(value)s δισεκατομμύρια"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f τρισεκατομμύριο"
msgstr[1] "%(value).1f τρισεκατομμύρια"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s τρισεκατομμύριο"
msgstr[1] "%(value)s τρισεκατομμύρια"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] "%(value).1f τετράκις εκατομμύριο"
msgstr[1] "%(value).1f τετράκις εκατομμύρια"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s τετράκις εκατομμύριο"
msgstr[1] "%(value)s τετράκις εκατομμύρια"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] "%(value).1f πεντάκις εκατομμύριo"
msgstr[1] "%(value).1f πεντάκις εκατομμύρια"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s πεντάκις εκατομμύριo"
msgstr[1] "%(value)s πεντάκις εκατομμύρια"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] "%(value).1f εξάκις εκατομμύριo"
msgstr[1] "%(value).1f εξάκις εκατομμύρια"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s εξάκις εκατομμύριo"
msgstr[1] "%(value)s εξάκις εκατομμύρια"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] "%(value).1f επτάκις εκατομμύριο"
msgstr[1] "%(value).1f επτάκις εκατομμύρια"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s επτάκις εκατομμύριο"
msgstr[1] "%(value)s επτάκις εκατομμύρια"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] "%(value).1f οκτάκις εκατομμύριο"
msgstr[1] "%(value).1f οκτάκις εκατομμύρια"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s οκτάκις εκατομμύριο"
msgstr[1] "%(value)s οκτάκις εκατομμύρια"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] "%(value).1f εννεάκις εκατομμύριο"
msgstr[1] "%(value).1f εννεάκις εκατομμύρια"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s εννεάκις εκατομμύριο"
msgstr[1] "%(value)s εννεάκις εκατομμύρια"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] "%(value).1f δεκάκις εκατομμύριο"
msgstr[1] "%(value).1f δεκάκις εκατομμύρια"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s δεκάκις εκατομμύριο"
msgstr[1] "%(value)s δεκάκις εκατομμύρια"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] "%(value).1f googol"
msgstr[1] "%(value).1f googol"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googol"

msgid "one"
msgstr "ένα"

msgid "two"
msgstr "δύο"

msgid "three"
msgstr "τρία"

msgid "four"
msgstr "τέσσερα"

msgid "five"
msgstr "πέντε"

msgid "six"
msgstr "έξι"

msgid "seven"
msgstr "εφτά"

msgid "eight"
msgstr "οκτώ"

msgid "nine"
msgstr "εννιά"

msgid "today"
msgstr "σήμερα"

msgid "tomorrow"
msgstr "αύριο"

msgid "yesterday"
msgstr "χθες"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "πριν από %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "μια ώρα πρίν"
msgstr[1] "%(count)s ώρες πρίν"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "ένα λεπτό πρίν"
msgstr[1] "%(count)s λεπτά πρίν"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "ένα δευτερόλεπτο πρίν"
msgstr[1] "%(count)s δευτερόλεπτα πρίν"

msgid "now"
msgstr "τώρα"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "ένα δευτερόλεπτο από τώρα"
msgstr[1] "%(count)s δευτερόλεπτα από τώρα"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "ένα λεπτό από τώρα"
msgstr[1] "%(count)s λεπτά από τώρα"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "μία ώρα από τώρα"
msgstr[1] "%(count)s ώρες από τώρα"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "σε %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d χρόνος"
msgstr[1] "%d χρόνια"

#, python-format
msgctxt "naturaltime-past"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d μήνας"
msgstr[1] "%d μήνες"

#, python-format
msgctxt "naturaltime-past"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d εβδομάδα"
msgstr[1] "%d εβδομάδες"

#, python-format
msgctxt "naturaltime-past"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d μέρα"
msgstr[1] "%d μέρες"

#, python-format
msgctxt "naturaltime-past"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d ώρα"
msgstr[1] "%d ώρες"

#, python-format
msgctxt "naturaltime-past"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d λεπτό"
msgstr[1] "%d λεπτά"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d χρόνος"
msgstr[1] "%d χρόνια"

#, python-format
msgctxt "naturaltime-future"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d μήνας"
msgstr[1] "%d μήνες"

#, python-format
msgctxt "naturaltime-future"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d εβδομάδα"
msgstr[1] "%d εβδομάδες"

#, python-format
msgctxt "naturaltime-future"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d μέρα"
msgstr[1] "%d μέρες"

#, python-format
msgctxt "naturaltime-future"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d ώρα"
msgstr[1] "%d ώρες"

#, python-format
msgctxt "naturaltime-future"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d λεπτό"
msgstr[1] "%d λεπτά"
