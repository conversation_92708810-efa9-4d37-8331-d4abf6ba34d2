# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2015-2018
# M <PERSON><PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-05-12 20:01+0000\n"
"Last-Translator: Transifex Bot <>\n"
"Language-Team: Indonesian (http://www.transifex.com/django/django/language/"
"id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "PostgreSQL extensions"
msgstr "Ekstensi PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Butir %(nth)s dalam larik tidak tervalidasi:"

msgid "Nested arrays must have the same length."
msgstr "Array bersaran harus mempunyai panjang yang sama."

msgid "Map of strings to strings/nulls"
msgstr "Pemetaan dari string ke string/null"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Nilai dari “%(key)s” bukan sebuah string atau null."

msgid "Could not load JSON data."
msgstr "Tidak dapat memuat data JSON."

msgid "Input must be a JSON dictionary."
msgstr "Masukan harus kamus JSON."

msgid "Enter two valid values."
msgstr "Masukkan dua nilai yang valid."

msgid "The start of the range must not exceed the end of the range."
msgstr "Awal jangkauan harus tidak melebihi akhir jangkauan."

msgid "Enter two whole numbers."
msgstr "Masukkan dua buah bilangan bulat."

msgid "Enter two numbers."
msgstr "Masukkan dua buah bilangan."

msgid "Enter two valid date/times."
msgstr "Masukan dua buah tanggal/waktu."

msgid "Enter two valid dates."
msgstr "Masukan dua buah tanggal yang benar."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Daftar mengandung %(show_value)d butir, seharusnya mengandung tidak lebih "
"dari %(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Daftar mengandung%(show_value)d butir, seharusnya mengandung tidak kurang "
"dari %(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Beberapa kunci hilang: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Beberapa kunci yang tidak dikenali diberikan: %(keys)s"

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr ""
"Pastikan bahwa jangkauan ini sepenuhnya kurang dari atau sama dengan "
"%(limit_value)s."

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr ""
"Pastikan bahwa jangkauan ini sepenuhnya lebih dari atau sama dengan "
"%(limit_value)s."
