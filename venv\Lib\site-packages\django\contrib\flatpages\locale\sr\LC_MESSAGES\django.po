# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2019-2020
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011-2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-01-21 20:41+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Serbian (http://www.transifex.com/django/django/language/"
"sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

msgid "Advanced options"
msgstr "Напредна подешавања"

msgid "Flat Pages"
msgstr "Флет странице"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Пример: \"/about/contact/\". Пазите на то да постоје почетне и завршне косе "
"црте."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Унета вредност може садржати само слова, бројке, тачке, доње црте, црте, "
"знаке разломка или тилде."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Пример: '/about/contact/'. Пазите на то да постоји почетна коса црта."

msgid "URL is missing a leading slash."
msgstr "Недостаје коса црта на почетку URL-а."

msgid "URL is missing a trailing slash."
msgstr "Недостаје коса црта на крају URL-а."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Страница са адресом %(url)s већ постоји за сајт %(site)s"

msgid "title"
msgstr "наслов"

msgid "content"
msgstr "садржај"

msgid "enable comments"
msgstr "омогући коментарисање"

msgid "template name"
msgstr "назив темплејта"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Пример: \"flatpages/contact_page.html\". Ако ово оставите празним, систем ће "
"користити \"flatpages/default.html\"."

msgid "registration required"
msgstr "потребна регистрација"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Ако је ово обележено, само ће пријављени корисници моћи да виде ову страницу."

msgid "sites"
msgstr "сајтови"

msgid "flat page"
msgstr "флет страница"

msgid "flat pages"
msgstr "флет странице"
