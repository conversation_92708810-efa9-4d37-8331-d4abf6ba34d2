# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Ossetic (http://www.transifex.com/django/django/language/"
"os/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: os\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Humanize"
msgstr ""

msgid "th"
msgstr "-ӕм"

msgid "st"
msgstr "-аг"

msgid "nd"
msgstr "-аг"

msgid "rd"
msgstr "-аг"

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f милуан"
msgstr[1] "%(value).1f милуан"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s милуан"
msgstr[1] "%(value)s милуан"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f биллон"
msgstr[1] "%(value).1f биллион"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s биллион"
msgstr[1] "%(value)s биллион"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f триллион"
msgstr[1] "%(value).1f триллион"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s триллион"
msgstr[1] "%(value)s триллион"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] "%(value).1f къуадриллион"
msgstr[1] "%(value).1f къуадриллион"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s къуадриллион"
msgstr[1] "%(value)s къуадриллион"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] "%(value).1f къуинтиллион"
msgstr[1] "%(value).1f къуинтиллион"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s къуинтиллион"
msgstr[1] "%(value)s къуинтиллион"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] "%(value).1f секстиллион"
msgstr[1] "%(value).1f секстиллион"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s секстиллион"
msgstr[1] "%(value)s секстиллион"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] "%(value).1f септиллион"
msgstr[1] "%(value).1f септиллион"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s септиллион"
msgstr[1] "%(value)s септиллион"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] "%(value).1f октиллион"
msgstr[1] "%(value).1f октиллион"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s октиллион"
msgstr[1] "%(value)s октиллион"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] "%(value).1f нониллион"
msgstr[1] "%(value).1f нониллион"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s нониллион"
msgstr[1] "%(value)s нониллион"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] "%(value).1f дециллион"
msgstr[1] "%(value).1f дециллион"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s дециллион"
msgstr[1] "%(value)s дециллион"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] "%(value).1f гугол"
msgstr[1] "%(value).1f гугол"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s гугол"
msgstr[1] "%(value)s гугол"

msgid "one"
msgstr "иу"

msgid "two"
msgstr "дыууӕ"

msgid "three"
msgstr "ӕртӕ"

msgid "four"
msgstr "цыппар"

msgid "five"
msgstr "фондз"

msgid "six"
msgstr "ӕхсӕз"

msgid "seven"
msgstr "авд"

msgid "eight"
msgstr "аст"

msgid "nine"
msgstr "фараст"

msgid "today"
msgstr "абон"

msgid "tomorrow"
msgstr "сом"

msgid "yesterday"
msgstr "знон"

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s ago"
msgstr "%(delta)s раздӕр"

msgid "now"
msgstr "ныр"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s from now"
msgstr "%(delta)s енырӕй"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] ""
msgstr[1] ""
