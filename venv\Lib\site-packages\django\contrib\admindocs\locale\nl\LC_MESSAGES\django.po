# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON> <ilja<PERSON><PERSON>@dreamsolution.nl>, 2015
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# 6a27f10aef159701c7a5ff07f0fb0a78_05545ed <bc5d401a7ecd9343dd5afac265ed8ab3_4845>, 2012,2014
# <AUTHOR> <EMAIL>, 2019,2022
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2023-04-24 20:19+0000\n"
"Last-Translator: Tonnes <<EMAIL>>, 2019,2022\n"
"Language-Team: Dutch (http://www.transifex.com/django/django/language/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Beheerdocumentatie"

msgid "Home"
msgstr "Voorpagina"

msgid "Documentation"
msgstr "Documentatie"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Documentatiebookmarklets"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Om bookmarklets te installeren, sleept u de koppeling naar uw "
"bladwijzerwerkbalk, of klikt u met de rechtermuisknop op de koppeling en "
"voegt u deze toe aan uw bladwijzers. De bookmarklet kan nu vanuit elke "
"pagina op de website worden geselecteerd."

msgid "Documentation for this page"
msgstr "Documentatie voor deze pagina"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Springt vanuit elke pagina naar de documentatie voor de weergave die die "
"pagina genereert."

msgid "Tags"
msgstr "Tags"

msgid "List of all the template tags and their functions."
msgstr "Overzicht van alle sjabloontags en hun functies."

msgid "Filters"
msgstr "Filters"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filters zijn acties die op variabelen in een sjabloon kunnen worden "
"toegepast om de uitvoer aan te passen."

msgid "Models"
msgstr "Modellen"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modellen zijn beschrijvingen van alle objecten in het systeem en hun "
"gekoppelde velden. Elk model heeft een lijst van velden die als "
"sjabloonvariabelen kunnen worden gekoppeld."

msgid "Views"
msgstr "Weergaven"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Elke pagina op de publieke website wordt gegenereerd door een weergave. De "
"weergave bepaalt welke sjabloon wordt gebruikt om de pagina te genereren en "
"welke objecten in die sjabloon beschikbaar zijn."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "Snelkoppelingen in je browser om makkelijk beheerpagina's te openen."

msgid "Please install docutils"
msgstr "Docutils is niet geïnstalleerd"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"Het beheerdocumentatiesysteem vereist de <a href=\"%(link)s\">docutils</a>-"
"bibliotheek van Python."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Vraag de beheerder om <a href=\"%(link)s\">docutils</a> te installeren."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Velden"

msgid "Field"
msgstr "Veld"

msgid "Type"
msgstr "Type"

msgid "Description"
msgstr "Beschrijving"

msgid "Methods with arguments"
msgstr "Methode met argumenten"

msgid "Method"
msgstr "Methode"

msgid "Arguments"
msgstr "Argumenten"

msgid "Back to Model documentation"
msgstr "Terug naar Modeldocumentatie"

msgid "Model documentation"
msgstr "Modeldocumentatie"

msgid "Model groups"
msgstr "Modules"

msgid "Templates"
msgstr "Sjablonen"

#, python-format
msgid "Template: %(name)s"
msgstr "Sjabloon: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Sjabloon: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Zoekpad voor sjabloon <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(bestaat niet)"

msgid "Back to Documentation"
msgstr "Terug naar Documentatie"

msgid "Template filters"
msgstr "Sjabloonfilters"

msgid "Template filter documentation"
msgstr "Sjabloontagdocumentatie "

msgid "Built-in filters"
msgstr "Standaardfilters"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Om deze filters te gebruiken, plaatst u <code>%(code)s</code> in uw sjabloon "
"voordat u het filter gebruikt."

msgid "Template tags"
msgstr "Sjabloontags"

msgid "Template tag documentation"
msgstr "Sjabloontagdocumentatie"

msgid "Built-in tags"
msgstr "Standaardtags"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Om deze tags te gebruiken, plaatst u <code>%(code)s</code> in uw sjabloon "
"voordat u de tag gebruikt."

#, python-format
msgid "View: %(name)s"
msgstr "Weergave: %(name)s"

msgid "Context:"
msgstr "Contextvariabelen:"

msgid "Templates:"
msgstr "Sjablonen:"

msgid "Back to View documentation"
msgstr "Terug naar Weergavedocumentatie"

msgid "View documentation"
msgstr "Weergavedocumentatie"

msgid "Jump to namespace"
msgstr "Naar namespace springen"

msgid "Empty namespace"
msgstr "Lege namespace"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Weergaven per namespace %(name)s"

msgid "Views by empty namespace"
msgstr "Weergaven per lege namespace"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Weergavefunctie: <code>%(full_name)s</code>. Naam: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "weergave:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "App %(app_label)r niet gevonden"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r niet gevonden in module %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "het gerelateerde `%(app_label)s.%(data_type)s` object"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "gerelateerde `%(app_label)s.%(object_name)s` objecten"

#, python-format
msgid "all %s"
msgstr "alle %s"

#, python-format
msgid "number of %s"
msgstr "aantal %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s lijkt geen urlpattern-object te zijn"
