# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2012,2014
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON>los <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-04-28 20:05+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Catalan (http://www.transifex.com/django/django/language/"
"ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Tipus de Contingut"

msgid "python model class name"
msgstr "nom de la classe del model en python"

msgid "content type"
msgstr "tipus de contingut"

msgid "content types"
msgstr "tipus de continguts"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "L'objecte del tipus de contingut %(ct_id)s no té un model associat"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "L'objecte %(obj_id)s del tipus de contingut %(ct_id)s no existeix"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Els objectes %(ct_name)s no tenen el mètode get_absolute_url()"
