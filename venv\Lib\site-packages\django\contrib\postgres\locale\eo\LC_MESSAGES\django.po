# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2015-2017,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-05-12 20:01+0000\n"
"Last-Translator: Transifex Bot <>\n"
"Language-Team: Esperanto (http://www.transifex.com/django/django/language/"
"eo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: eo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "PostgreSQL kromaĵoj"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Elemento %(nth)s en la tabelo ne estas valida:"

msgid "Nested arrays must have the same length."
msgstr "Ingitaj tabeloj devas havi la saman grandon."

msgid "Map of strings to strings/nulls"
msgstr "Kongruo de signoĉenoj al signoĉenoj/nulvaloroj"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr ""

msgid "Could not load JSON data."
msgstr "Malsukcesis ŝarĝi la JSON datumojn."

msgid "Input must be a JSON dictionary."
msgstr "La enigo devas esti JSON vortaro."

msgid "Enter two valid values."
msgstr "Enigu du validajn valorojn."

msgid "The start of the range must not exceed the end of the range."
msgstr "La komenco de la intervalo ne devas superi la finon de la intervalo."

msgid "Enter two whole numbers."
msgstr "Enigu du entjeroj."

msgid "Enter two numbers."
msgstr "Enigu du nombroj."

msgid "Enter two valid date/times."
msgstr "Enigu du validajn dato/horojn."

msgid "Enter two valid dates."
msgstr "Enigu du validajn datojn."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"La listo enhavas %(show_value)d eron, kaj ne devas enhavi pli ol "
"%(limit_value)d."
msgstr[1] ""
"La listo enhavas %(show_value)d erojn, kaj ne devas enhavi pli ol "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"La listo enhavas %(show_value)d erojn, kaj devas enhavi pli ol "
"%(limit_value)d."
msgstr[1] ""
"La listo enhavas %(show_value)d erojn, kaj devas enhavi pli ol "
"%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Kelkaj ŝlosiloj mankas: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Kelkaj nekonataj ŝlosiloj estis provizitaj: %(keys)s"

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr ""
"Bv kontroli, ke la tuta intervalo estas malpli alta aŭ egala al "
"%(limit_value)s."

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr ""
"Bv kontroli, ke la tuta intervalo estas pli alta aŭ egala al %(limit_value)s."
