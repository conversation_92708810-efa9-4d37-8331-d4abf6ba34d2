# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2011
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011,2015
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2012
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-03-04 03:38+0000\n"
"Last-Translator: wolf ice <<EMAIL>>\n"
"Language-Team: Chinese (China) (http://www.transifex.com/django/django/"
"language/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "GIS"
msgstr "GIS"

msgid "The base GIS field."
msgstr "基础 GIS 字段。"

msgid ""
"The base Geometry field — maps to the OpenGIS Specification Geometry type."
msgstr "基本几何字段—映射到OpenGIS规范几何类型。"

msgid "Point"
msgstr "点"

msgid "Line string"
msgstr "行字串"

msgid "Polygon"
msgstr "多边形"

msgid "Multi-point"
msgstr "多点"

msgid "Multi-line string"
msgstr "多行字符串"

msgid "Multi polygon"
msgstr "多个多边形"

msgid "Geometry collection"
msgstr "几何集合"

msgid "Extent Aggregate Field"
msgstr "扩展聚集字段"

msgid "Raster Field"
msgstr "光栅字段"

msgid "No geometry value provided."
msgstr "未提供几何信息。"

msgid "Invalid geometry value."
msgstr "无效几何信息。"

msgid "Invalid geometry type."
msgstr "无效几何类型。"

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr "几何形状转换SRID字段将发生错误"

msgid "Delete all Features"
msgstr "删除所有要素"

msgid "WKT debugging window:"
msgstr "WKT 调试窗口："

msgid "Debugging window (serialized value)"
msgstr "调试窗口(已序列化的值)"

msgid "No feeds are registered."
msgstr "没有已注册的源。"

#, python-format
msgid "Slug %r isn’t registered."
msgstr "字段 %r没被注册"
