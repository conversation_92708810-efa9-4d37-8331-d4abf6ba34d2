# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2016,2019
# <PERSON><PERSON><PERSON>o <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2015
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2023-04-19 09:22+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Portuguese (Brazil) (http://www.transifex.com/django/django/"
"language/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "PostgreSQL extensions"
msgstr "Extensões para PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "O item %(nth)s na matriz não validou:"

msgid "Nested arrays must have the same length."
msgstr "Matrizes aninhadas devem ter o mesmo comprimento."

msgid "Map of strings to strings/nulls"
msgstr "Mapa de strings para strings/nulls"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "O valor da “%(key)s” não é string ou null."

msgid "Could not load JSON data."
msgstr "Não foi possível carregar dados JSON."

msgid "Input must be a JSON dictionary."
msgstr "Input deve ser um dicionário JSON"

msgid "Enter two valid values."
msgstr "Insira dois valores válidos."

msgid "The start of the range must not exceed the end of the range."
msgstr "O inicio do intervalo não deve exceder o fim do intervalo."

msgid "Enter two whole numbers."
msgstr "Insira dois números cheios."

msgid "Enter two numbers."
msgstr "Insira dois números"

msgid "Enter two valid date/times."
msgstr "Insira duas datas/horas válidas."

msgid "Enter two valid dates."
msgstr "Insira duas datas válidas."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"A lista contém um item %(show_value)d, não deveria conter mais que "
"%(limit_value)d."
msgstr[1] ""
"A lista contém itens %(show_value)d, não deveria conter mais que "
"%(limit_value)d."
msgstr[2] ""
"A lista contém itens %(show_value)d, não deveria conter mais que "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"A lista contém um item %(show_value)d, deveria conter não menos que "
"%(limit_value)d."
msgstr[1] ""
"A lista contém %(show_value)d itens, deveria conter não menos que "
"%(limit_value)d."
msgstr[2] ""
"A lista contém %(show_value)d itens, deveria conter não menos que "
"%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Algumas chaves estavam faltando: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Algumas chaves desconhecidas foram fornecidos: %(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr ""
"Certifique-se de que o limite superior do intervalo não seja maior que "
"%(limit_value)s."

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr ""
"Assegure-se de que o limite inferior do intervalo não seja menor que "
"%(limit_value)s."
