# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tamil (http://www.transifex.com/django/django/language/ta/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ta\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr ""

msgid "Home"
msgstr "வீடு"

msgid "Documentation"
msgstr "ஆவனமாக்கம்"

msgid "Bookmarklets"
msgstr "புத்தகக்குறிகள்"

msgid "Documentation bookmarklets"
msgstr "ஆவணமாக்கக் குறியீடுகள்"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""

msgid "Documentation for this page"
msgstr "இந்த பக்கத்திற்கான ஆவணம்"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"எந்த ஒரு பக்கத்திலிருந்தும் ஆவணப்பக்கத்தை பார்வையிடுதல், அந்த பக்கத்தை உருவாக்குகிறது."

msgid "Tags"
msgstr ""

msgid "List of all the template tags and their functions."
msgstr ""

msgid "Filters"
msgstr ""

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""

msgid "Models"
msgstr ""

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

msgid "Views"
msgstr ""

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

msgid "Please install docutils"
msgstr ""

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#, python-format
msgid "Model: %(name)s"
msgstr ""

msgid "Fields"
msgstr ""

msgid "Field"
msgstr ""

msgid "Type"
msgstr ""

msgid "Description"
msgstr ""

msgid "Methods with arguments"
msgstr ""

msgid "Method"
msgstr ""

msgid "Arguments"
msgstr ""

msgid "Back to Model documentation"
msgstr ""

msgid "Model documentation"
msgstr ""

msgid "Model groups"
msgstr ""

msgid "Templates"
msgstr ""

#, python-format
msgid "Template: %(name)s"
msgstr ""

#, python-format
msgid "Template: \"%(name)s\""
msgstr ""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr ""

msgid "(does not exist)"
msgstr ""

msgid "Back to Documentation"
msgstr ""

msgid "Template filters"
msgstr ""

msgid "Template filter documentation"
msgstr ""

msgid "Built-in filters"
msgstr ""

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr ""

msgid "Template tag documentation"
msgstr ""

msgid "Built-in tags"
msgstr ""

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr ""

msgid "Context:"
msgstr ""

msgid "Templates:"
msgstr ""

msgid "Back to View documentation"
msgstr ""

msgid "View documentation"
msgstr ""

msgid "Jump to namespace"
msgstr ""

msgid "Empty namespace"
msgstr ""

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "tag:"
msgstr "ஒட்டு:"

msgid "filter:"
msgstr "வடிகட்டி:"

msgid "view:"
msgstr "நோற்றமிடு:"

#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""

msgid "model:"
msgstr "மாதிரி:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr ""

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr ""

#, python-format
msgid "all %s"
msgstr "அனைத்து %s "

#, python-format
msgid "number of %s"
msgstr "எண்ணிக்கை %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s -ல் urlpattern தோன்றுவதில்லை"
