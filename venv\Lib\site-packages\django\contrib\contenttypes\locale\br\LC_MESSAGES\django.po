# This file is distributed under the same license as the Django package.
#
# Translators:
# Irriep <PERSON> <<EMAIL>>, 2018-2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2019-03-12 14:31+0000\n"
"Last-Translator: Irriep <PERSON> <<EMAIL>>\n"
"Language-Team: Breton (http://www.transifex.com/django/django/language/br/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: br\n"
"Plural-Forms: nplurals=5; plural=((n%10 == 1) && (n%100 != 11) && (n%100 !"
"=71) && (n%100 !=91) ? 0 :(n%10 == 2) && (n%100 != 12) && (n%100 !=72) && (n"
"%100 !=92) ? 1 :(n%10 ==3 || n%10==4 || n%10==9) && (n%100 < 10 || n% 100 > "
"19) && (n%100 < 70 || n%100 > 79) && (n%100 < 90 || n%100 > 99) ? 2 :(n != 0 "
"&& n % 1000000 == 0) ? 3 : 4);\n"

msgid "Content Types"
msgstr "Doareoù endalc'had"

msgid "python model class name"
msgstr "anv klas model python"

msgid "content type"
msgstr "doare endalc'had"

msgid "content types"
msgstr "doareoù endalc'had"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Doare endalc'had an objed %(ct_id)s n'eus tamm skouer kevelet gantañ"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Doare endalc'had %(ct_id)s an objed %(obj_id)s n'eus ket anezhañ"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "An objedoù %(ct_name)s n'o deus ket un hentenn get_absolute_url()"
