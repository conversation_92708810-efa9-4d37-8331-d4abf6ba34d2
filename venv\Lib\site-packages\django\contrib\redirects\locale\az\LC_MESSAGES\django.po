# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-01-12 06:46+0000\n"
"Last-Translator: Emin Mast<PERSON> <<EMAIL>>\n"
"Language-Team: Azerbaijani (http://www.transifex.com/django/django/language/"
"az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Yönlə<PERSON>rmələr"

msgid "site"
msgstr "sayt"

msgid "redirect from"
msgstr "buradan yönəlt"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr "Bu, domen adı xaric, mütləq yol olmalıdır. Məsələn: “/events/search/”."

msgid "redirect to"
msgstr "bura yönəlt"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with "
"“http://”."
msgstr ""
"Bu həm mütləq yol (yuxarıdakı kimi), həm də “http://” ilə başlayan tam URL "
"ola bilər."

msgid "redirect"
msgstr "yönəlt"

msgid "redirects"
msgstr "yönəldir"
