# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# angularcircle, 2011
# angularcircle, 2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2018,2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <PERSON>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-04-07 14:40+0200\n"
"PO-Revision-Date: 2021-09-23 19:40+0000\n"
"Last-Translator: m_aciek <<EMAIL>>\n"
"Language-Team: Polish (http://www.transifex.com/django/django/language/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n"
"%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n"
"%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

msgid "Humanize"
msgstr "Humanizacja"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}."

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}."

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s milion"
msgstr[1] "%(value)s miliony"
msgstr[2] "%(value)s milionów"
msgstr[3] "%(value)s milionów"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s miliard"
msgstr[1] "%(value)s miliardy"
msgstr[2] "%(value)s miliardów"
msgstr[3] "%(value)s miliardów"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s kwintylion"
msgstr[1] "%(value)s biliony"
msgstr[2] "%(value)s kwintylionów"
msgstr[3] "%(value)s kwintylionów"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s kwadrylion"
msgstr[1] "%(value)s biliardy"
msgstr[2] "%(value)s kwadrylionów"
msgstr[3] "%(value)s kwadrylionów"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s trylion"
msgstr[1] "%(value)s tryliony"
msgstr[2] "%(value)s trylionyów"
msgstr[3] "%(value)s trylionyów"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] " %(value)s tryliard"
msgstr[1] "%(value)s tryliardy"
msgstr[2] "%(value)s tryliardów"
msgstr[3] "%(value)s tryliardów"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septylion"
msgstr[1] "%(value)s septyliony"
msgstr[2] "%(value)s septylionów"
msgstr[3] "%(value)s septylionów"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s kwadryliard"
msgstr[1] "%(value)s kwardyliardy"
msgstr[2] "%(value)s kwadryliardów"
msgstr[3] "%(value)s kwadryliardów"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s kwintylion"
msgstr[1] "%(value)s kwintyliony"
msgstr[2] "%(value)s kwintylionów"
msgstr[3] "%(value)s kwintylionów"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s kwintyliard"
msgstr[1] "%(value)s kwintyliardy"
msgstr[2] "%(value)s kwintyliardów"
msgstr[3] "%(value)s kwintyliardów"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googole"
msgstr[2] "%(value)s googolów"
msgstr[3] "%(value)s googolów"

msgid "one"
msgstr "jeden"

msgid "two"
msgstr "dwa"

msgid "three"
msgstr "trzy"

msgid "four"
msgstr "cztery"

msgid "five"
msgstr "pięć"

msgid "six"
msgstr "sześć"

msgid "seven"
msgstr "siedem"

msgid "eight"
msgstr "osiem"

msgid "nine"
msgstr "dziewięć"

msgid "today"
msgstr "dzisiaj"

msgid "tomorrow"
msgstr "jutro"

msgid "yesterday"
msgstr "wczoraj"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s temu"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "godzinę temu"
msgstr[1] "%(count)s godziny temu"
msgstr[2] "%(count)s godzin temu"
msgstr[3] "%(count)s godzin temu"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "minutę temu"
msgstr[1] "%(count)s minuty temu"
msgstr[2] "%(count)s minut temu"
msgstr[3] "%(count)s minut temu"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "sekundę temu"
msgstr[1] "%(count)s sekundy temu"
msgstr[2] "%(count)s sekund temu"
msgstr[3] "%(count)s sekund temu"

msgid "now"
msgstr "teraz"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "za sekundę"
msgstr[1] "za %(count)s sekundy"
msgstr[2] "za %(count)s sekund"
msgstr[3] "za %(count)s sekund"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "za minutę"
msgstr[1] "za %(count)s minuty"
msgstr[2] "za %(count)s minut"
msgstr[3] "za %(count)s minut"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "za godzinę"
msgstr[1] "za %(count)s godziny"
msgstr[2] "za %(count)s godzin"
msgstr[3] "za %(count)s godzin"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "za %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d rok"
msgstr[1] "%(num)d lata"
msgstr[2] "%(num)d lat"
msgstr[3] "%(num)d roku"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d miesiąc"
msgstr[1] "%(num)d miesiące"
msgstr[2] "%(num)d miesięcy"
msgstr[3] "%(num)d miesiąca"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d tydzień"
msgstr[1] "%(num)d tygodnie"
msgstr[2] "%(num)d tygodni"
msgstr[3] "%(num)d tygodnia"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dzień"
msgstr[1] "%(num)d dni"
msgstr[2] "%(num)d dni"
msgstr[3] "%(num)d dnia"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d godzinę"
msgstr[1] "%(num)d godziny"
msgstr[2] "%(num)d godzin"
msgstr[3] "%(num)d godziny"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minutę"
msgstr[1] "%(num)d minuty"
msgstr[2] "%(num)d minut"
msgstr[3] "%(num)d minuty"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d rok"
msgstr[1] "%(num)d lata"
msgstr[2] "%(num)d lat"
msgstr[3] "%(num)d roku"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d miesiąc"
msgstr[1] "%(num)d miesiące"
msgstr[2] "%(num)d miesięcy"
msgstr[3] "%(num)dmiesiąca"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d tydzień"
msgstr[1] "%(num)d tygodnie"
msgstr[2] "%(num)d tygodni"
msgstr[3] "%(num)d tygodnia"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dzień"
msgstr[1] "%(num)d dni"
msgstr[2] "%(num)d dni"
msgstr[3] "%(num)d dnia"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d godzinę"
msgstr[1] "%(num)d godziny"
msgstr[2] "%(num)d godzin"
msgstr[3] "%(num)d godziny"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minutę"
msgstr[1] "%(num)d minuty"
msgstr[2] "%(num)d minut"
msgstr[3] "%(num)d minuty"
