# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2012-2013
# <PERSON> <<EMAIL>>, 2017-2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2019-01-31 12:54+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Slovak (http://www.transifex.com/django/django/language/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n "
">= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

msgid "Humanize"
msgstr "Poľudštenie"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}."

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}."

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}."

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}."

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f miliónu"
msgstr[1] "%(value).1f miliónu"
msgstr[2] "%(value).1f miliónu"
msgstr[3] "%(value).1f miliónu"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] " %(value)s milión"
msgstr[1] " %(value)s milióny"
msgstr[2] " %(value)s miliónov"
msgstr[3] " %(value)s miliónov"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f miliarda"
msgstr[1] "%(value).1f miliardy"
msgstr[2] "%(value).1f miliárd"
msgstr[3] "%(value).1f miliárd"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] " %(value)s miliarda"
msgstr[1] " %(value)s miliardy"
msgstr[2] " %(value)s miliárd"
msgstr[3] " %(value)s miliárd"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f bilión"
msgstr[1] "%(value).1f bilióny"
msgstr[2] "%(value).1f biliónov"
msgstr[3] "%(value).1f biliónov"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s bilión"
msgstr[1] "%(value)s bilióny"
msgstr[2] "%(value)s biliónov"
msgstr[3] "%(value)s biliónov"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] "%(value).1f biliardy"
msgstr[1] "%(value).1f biliardy"
msgstr[2] "%(value).1f biliárd"
msgstr[3] "%(value).1f biliárd"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s biliarda"
msgstr[1] "%(value)s biliardy"
msgstr[2] "%(value)s biliárd"
msgstr[3] "%(value)s biliárd"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] "%(value).1f triliónu"
msgstr[1] "%(value).1f triliónu"
msgstr[2] "%(value).1f triliónu"
msgstr[3] "%(value).1f triliónu"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s trilión"
msgstr[1] "%(value)s trilióny"
msgstr[2] "%(value)s triliónov"
msgstr[3] "%(value)s triliónov"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] "%(value).1f triliardy"
msgstr[1] "%(value).1f triliardy"
msgstr[2] "%(value).1f triliárd"
msgstr[3] "%(value).1f triliárd"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s triliarda"
msgstr[1] "%(value)s triliardy"
msgstr[2] "%(value)s triliárd"
msgstr[3] "%(value)s triliárd"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] "%(value).1f kvadriliónu"
msgstr[1] "%(value).1f kvadriliónu"
msgstr[2] "%(value).1f kvadriliónov"
msgstr[3] "%(value).1f kvadriliónov"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s kvadrilión"
msgstr[1] "%(value)s kvadrilióny"
msgstr[2] "%(value)s kvadriliónov"
msgstr[3] "%(value)s kvadriliónov"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] "%(value).1f kvadriliardy"
msgstr[1] "%(value).1f kvadriliardy"
msgstr[2] "%(value).1f kvadriliárd"
msgstr[3] "%(value).1f kvadriliárd"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s kvadriliarda"
msgstr[1] "%(value)s kvadriliardy"
msgstr[2] "%(value)s kvadriliárd"
msgstr[3] "%(value)s kvadriliárd"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] "%(value).1f kvintiliónu"
msgstr[1] "%(value).1f kvintiliónu"
msgstr[2] "%(value).1f kvintiliónov"
msgstr[3] "%(value).1f kvintiliónov"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s kvintilión"
msgstr[1] "%(value)s kvintilióny"
msgstr[2] "%(value)s kvintiliónov"
msgstr[3] "%(value)s kvintiliónov"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] "%(value).1f kvintiliardy"
msgstr[1] "%(value).1f kvintiliardy"
msgstr[2] "%(value).1f kvintiliárd"
msgstr[3] "%(value).1f kvintiliárd"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s kvintiliarda"
msgstr[1] "%(value)s kvintiliardy"
msgstr[2] "%(value)s kvintiliárd"
msgstr[3] "%(value)s kvintiliárd"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] "%(value).1f googol"
msgstr[1] "%(value).1f googol"
msgstr[2] "%(value).1f googol"
msgstr[3] "%(value).1f googol"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googol"
msgstr[2] "%(value)s googol"
msgstr[3] "%(value)s googol"

msgid "one"
msgstr "jeden"

msgid "two"
msgstr "dva"

msgid "three"
msgstr "tri"

msgid "four"
msgstr "štyri"

msgid "five"
msgstr "päť"

msgid "six"
msgstr "šesť"

msgid "seven"
msgstr "sedem"

msgid "eight"
msgstr "osem"

msgid "nine"
msgstr "deväť"

msgid "today"
msgstr "dnes"

msgid "tomorrow"
msgstr "zajtra"

msgid "yesterday"
msgstr "včera"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "pred %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "pred hodinou"
msgstr[1] "pred %(count)s hodinami"
msgstr[2] "pred %(count)s hodinami"
msgstr[3] "pred %(count)s hodinami"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "pred minútou"
msgstr[1] "pred %(count)s minútami"
msgstr[2] "pred %(count)s minútami"
msgstr[3] "pred %(count)s minútami"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "pred sekundou"
msgstr[1] "pred %(count)s sekundami"
msgstr[2] "pred %(count)s sekundami"
msgstr[3] "pred %(count)s sekundami"

msgid "now"
msgstr "teraz"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "o sekundu"
msgstr[1] "o %(count)s sekundy"
msgstr[2] "o %(count)s sekúnd"
msgstr[3] "o %(count)s sekúnd"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "o minútu"
msgstr[1] "o %(count)s minúty"
msgstr[2] "o %(count)s minút"
msgstr[3] "o %(count)s minút"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "o hodinu"
msgstr[1] "o %(count)s hodiny"
msgstr[2] "o %(count)s hodín"
msgstr[3] "o %(count)s hodín"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "o %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d rokom"
msgstr[1] "%d rokmi"
msgstr[2] "%d rokmi"
msgstr[3] "%d rokmi"

#, python-format
msgctxt "naturaltime-past"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d mesiacom"
msgstr[1] "%d mesiacmi"
msgstr[2] "%d mesiacmi"
msgstr[3] "%d mesiacmi"

#, python-format
msgctxt "naturaltime-past"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d týždeň"
msgstr[1] "%d týždne"
msgstr[2] "%d týždňami"
msgstr[3] "%d týždňami"

#, python-format
msgctxt "naturaltime-past"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dňom"
msgstr[1] "%d dňami"
msgstr[2] "%d dňami"
msgstr[3] "%d dňami"

#, python-format
msgctxt "naturaltime-past"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d hodinou"
msgstr[1] "%d hodinami"
msgstr[2] "%d hodinami"
msgstr[3] "%d hodinami"

#, python-format
msgctxt "naturaltime-past"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minútou"
msgstr[1] "%d minútami"
msgstr[2] "%d minútami"
msgstr[3] "%d minútami"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d rok"
msgstr[1] "%d roky"
msgstr[2] "%d rokov"
msgstr[3] "%d rokov"

#, python-format
msgctxt "naturaltime-future"
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d mesiac"
msgstr[1] "%d mesiace"
msgstr[2] "%d mesiacov"
msgstr[3] "%d mesiacov"

#, python-format
msgctxt "naturaltime-future"
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] "%d týždeň"
msgstr[1] "%d týždne"
msgstr[2] "%d týždňov"
msgstr[3] "%d týždňov"

#, python-format
msgctxt "naturaltime-future"
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d deň"
msgstr[1] "%d dni"
msgstr[2] "%d dní"
msgstr[3] "%d dní"

#, python-format
msgctxt "naturaltime-future"
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d hodina"
msgstr[1] "%d hodiny"
msgstr[2] "%d hodín"
msgstr[3] "%d hodín"

#, python-format
msgctxt "naturaltime-future"
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minúta"
msgstr[1] "%d minúty"
msgstr[2] "%d minút"
msgstr[3] "%d minút"
