# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-11-16 12:42+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: Malay (http://www.transifex.com/django/django/language/ms/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ms\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Content Types"
msgstr "Jenis kandungan"

msgid "python model class name"
msgstr "nama kelas model python"

msgid "content type"
msgstr "jenis kandungan"

msgid "content types"
msgstr "jenis-jenis kandungan"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "<PERSON><PERSON> kandungan %(ct_id)s tidak mempunyai model yang berkaitan"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Jenis kandungan %(ct_id)s objek %(obj_id)s tidak wujud"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Objek-objek %(ct_name)s tidak mempunyai kaedah get_absolute_url()"
