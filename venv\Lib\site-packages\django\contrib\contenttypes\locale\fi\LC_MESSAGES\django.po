# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2015,2020
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-12-09 06:31+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: Finnish (http://www.transifex.com/django/django/language/"
"fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Sisältötyypit"

msgid "python model class name"
msgstr "mallin python-luokan nimi"

msgid "content type"
msgstr "sisältötyyppi"

msgid "content types"
msgstr "sisältötyypit"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Sisältötyypin %(ct_id)s objektiin ei ole liitetty mallia"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Sisältötyypin %(ct_id)s objektia %(obj_id)s ei ole olemassa"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s-objekteilla ei ole get_absolute_url()-metodia"
