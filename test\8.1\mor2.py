from fastapi import FastAPI
from fastapi.responses import JSONResponse,HTMLResponse,FileResponse
app = FastAPI()

#post请求方式
# @app.post("/login")
# def login():
#     return {"msg":"login success"}

#多种请求方式
# @app.api_route("/login",methods=("get","post","put"))
# def logic():
#     return {"msg":"login success"}

# http://127.0.0.1:8000/user/8
# @app.get("/user/{id}")
# def user(id):
#     return{"id":id}

#http://127.0.0.1:8000/user?id=8
# @app.get("/user")
# def user(id):
#     return{"id":id}


# @app.get("/user")
# def user(id, token=Header(None)):#token=Header(None)默认参数
#     return{"id":id,"token":token}


# @app.post("/login")
# def login(data=Body(None)):#data随意命名
#     return{"data":data}

# @app.post("/login")
# def login(username=Form(None),password=Form(None)):#data随意命名
#     return{"uesrname":username, "password":password}

# @app.get("/user")
# def user():
#     return{"msg":"get user"}


@app.get("/user")
def user():
    return JSONResponse(content={"msg":"get user"},
                        status_code=202,
                        headers={"a":"b"})
@app.get("/")
def user():
    html_content = """
    <html>
    <body> <p style="color:red">hello world</p>  </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@app.get("/avatar")
def user():
    avatar = r'C:\Users\<USER>\Pictures\11.jpeg'
    return FileResponse(avatar,filename='11.jpg')