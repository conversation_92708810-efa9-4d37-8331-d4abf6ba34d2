# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2013
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
# <AUTHOR> <EMAIL>, 2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2023-04-24 19:22+0000\n"
"Last-Translator: X Bello <<EMAIL>>, 2023\n"
"Language-Team: Galician (http://www.transifex.com/django/django/language/"
"gl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: gl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Tipos de Contido"

msgid "python model class name"
msgstr "nome en Python da clase do modelo"

msgid "content type"
msgstr "tipo de contido"

msgid "content types"
msgstr "tipos de contido"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "O obxecto de tipo de contido %(ct_id)s non ten un modelo asociado"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "O obxecto %(obj_id)s con tipo de contido %(ct_id)s non existe"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Os obxectos %(ct_name)s non teñen un método get_absolute_url()"
