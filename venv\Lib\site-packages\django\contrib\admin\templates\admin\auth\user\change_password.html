{% extends "admin/base_site.html" %}
{% load i18n static %}
{% load admin_urls %}

{% block extrastyle %}{{ block.super }}<link rel="stylesheet" href="{% static "admin/css/forms.css" %}">{% endblock %}
{% block bodyclass %}{{ block.super }} {{ opts.app_label }}-{{ opts.model_name }} change-form{% endblock %}
{% if not is_popup %}
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
&rsaquo; <a href="{% url opts|admin_urlname:'change' original.pk|admin_urlquote %}">{{ original|truncatewords:"18" }}</a>
&rsaquo; {% translate 'Change password' %}
</div>
{% endblock %}
{% endif %}
{% block content %}<div id="content-main">
<form{% if form_url %} action="{{ form_url }}"{% endif %} method="post" id="{{ opts.model_name }}_form">{% csrf_token %}{% block form_top %}{% endblock %}
<input type="text" name="username" value="{{ original.get_username }}" class="hidden">
<div>
{% if is_popup %}<input type="hidden" name="{{ is_popup_var }}" value="1">{% endif %}
{% if form.errors %}
    <p class="errornote">
    {% blocktranslate count counter=form.errors.items|length %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktranslate %}
    </p>
{% endif %}

<p>{% blocktranslate with username=original %}Enter a new password for the user <strong>{{ username }}</strong>.{% endblocktranslate %}</p>

<fieldset class="module aligned">

<div class="form-row">
  {{ form.password1.errors }}
  <div class="flex-container">{{ form.password1.label_tag }} {{ form.password1 }}</div>
  {% if form.password1.help_text %}
  <div class="help"{% if form.password1.id_for_label %} id="{{ form.password1.id_for_label }}_helptext">{% endif %}{{ form.password1.help_text|safe }}</div>
  {% endif %}
</div>

<div class="form-row">
  {{ form.password2.errors }}
  <div class="flex-container">{{ form.password2.label_tag }} {{ form.password2 }}</div>
  {% if form.password2.help_text %}
  <div class="help"{% if form.password2.id_for_label %} id="{{ form.password2.id_for_label }}_helptext"{% endif %}>{{ form.password2.help_text|safe }}</div>
  {% endif %}
</div>

</fieldset>

<div class="submit-row">
<input type="submit" value="{% translate 'Change password' %}" class="default">
</div>

</div>
</form></div>
{% endblock %}
