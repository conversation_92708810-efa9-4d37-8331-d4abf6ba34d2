# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2016,2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-04 07:05+0000\n"
"Last-Translator: Pãnoș <<EMAIL>>\n"
"Language-Team: Greek (http://www.transifex.com/django/django/language/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Τύποι Περιεχομένου"

msgid "python model class name"
msgstr "όνομα κλάσης μοντέλου python"

msgid "content type"
msgstr "τύπος περιεχομένου"

msgid "content types"
msgstr "τύποι περιεχομένου"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr ""
"Το αντικείμενο %(ct_id)s τύπου περιεχομένου δεν έχει συσχετισμένο μοντέλο"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Το αντικείμενο %(obj_id)s τύπου περιεχομένου %(ct_id)s δεν υπάρχει"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "τα αντικείμενα %(ct_name)s δεν έχουν μέθοδο get_absolute_url()"
