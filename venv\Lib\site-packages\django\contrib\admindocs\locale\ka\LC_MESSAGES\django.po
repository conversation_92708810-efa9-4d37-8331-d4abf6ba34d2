# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013,2015
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Georgian (http://www.transifex.com/django/django/language/"
"ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

msgid "Administrative Documentation"
msgstr "ადმინისტრირების დოკუმენტაცია"

msgid "Home"
msgstr "საწყისი გვერდი"

msgid "Documentation"
msgstr "დოკუმენტაცია"

msgid "Bookmarklets"
msgstr "სანიშნები"

msgid "Documentation bookmarklets"
msgstr "დოკუმენტაციის სანიშნები"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""

msgid "Documentation for this page"
msgstr "ამ გვერდის დოკუმენტაცია"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"გადასვლა ნებისმიერი გვერდიდან იმ წარმოდგენის დოკუმენტაციაზე, რომელიც ამ "
"გვერდს აგენერირებს."

msgid "Tags"
msgstr "ტეგები"

msgid "List of all the template tags and their functions."
msgstr "ყველა შაბლონის ტეგის სია და მათი ფუნქციები."

msgid "Filters"
msgstr "ფილტრები"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"ფილტრები მოქმედებებია, რომელებიც შეიძლება გამოიყენებოდეს ცვლადებზე შაბლონში "
"გამონატანის შესაცვლელად."

msgid "Models"
msgstr "მოდელები"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

msgid "Views"
msgstr "წარმოდგენები"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

msgid "Please install docutils"
msgstr "გთხოვთ, დააყენოთ docutils"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#, python-format
msgid "Model: %(name)s"
msgstr "მოდელი: %(name)s"

msgid "Fields"
msgstr ""

msgid "Field"
msgstr "ველი"

msgid "Type"
msgstr "ტიპი"

msgid "Description"
msgstr "აღწერა"

msgid "Methods with arguments"
msgstr ""

msgid "Method"
msgstr ""

msgid "Arguments"
msgstr ""

msgid "Back to Model documentation"
msgstr ""

msgid "Model documentation"
msgstr "მოდელის დოკუმენტაცია"

msgid "Model groups"
msgstr ""

msgid "Templates"
msgstr "შაბლონები"

#, python-format
msgid "Template: %(name)s"
msgstr "შაბლონი: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "შაბლონი: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr ""

msgid "(does not exist)"
msgstr "(არ არსებობს)"

msgid "Back to Documentation"
msgstr ""

msgid "Template filters"
msgstr "შაბლონის ფილტრები"

msgid "Template filter documentation"
msgstr ""

msgid "Built-in filters"
msgstr ""

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr "შაბლონის ტეგები"

msgid "Template tag documentation"
msgstr "შაბლონის ტეგების დოკუმენტაცია"

msgid "Built-in tags"
msgstr ""

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr "ხედი: %(name)s"

msgid "Context:"
msgstr "კონტექსტი"

msgid "Templates:"
msgstr "შაბლონები:"

msgid "Back to View documentation"
msgstr ""

msgid "View documentation"
msgstr "ხედების დოკუმენტაცია"

msgid "Jump to namespace"
msgstr ""

msgid "Empty namespace"
msgstr ""

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "tag:"
msgstr "ტეგი:"

msgid "filter:"
msgstr "ფილტრი:"

msgid "view:"
msgstr "წარმოდგენა:"

#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "მოდელი %(model_name)r ვერ მოიძებნა აპლიკაციაში %(app_label)r"

msgid "model:"
msgstr "მოდელი:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "`%(app_label)s.%(data_type)s` დაკავშირებული ობიექტი"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "`%(app_label)s.%(object_name)s` დაკავშირებული ობიექტები"

#, python-format
msgid "all %s"
msgstr "ყველა %s"

#, python-format
msgid "number of %s"
msgstr "%s - რაოდენობა"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s არ არის urlpattern-ის შესაბამისი ობიექტი"
