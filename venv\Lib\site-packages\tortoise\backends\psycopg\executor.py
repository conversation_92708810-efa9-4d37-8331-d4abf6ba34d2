from __future__ import annotations

from pypika_tortoise import Parameter

from tortoise import Model
from tortoise.backends.base_postgres.executor import BasePostgresExecutor


class PsycopgExecutor(BasePostgresExecutor):
    async def _process_insert_result(self, instance: Model, results: dict | tuple | None) -> None:
        if results:
            db_projection = instance._meta.fields_db_projection_reverse

            if isinstance(results, dict):
                for key, val in results.items():
                    setattr(instance, db_projection[key], val)
            else:
                generated_fields = self.model._meta.generated_db_fields

                for key, val in zip(generated_fields, results):
                    setattr(instance, db_projection[key], val)

    def parameter(self, pos: int) -> Parameter:
        return Parameter("%s")
