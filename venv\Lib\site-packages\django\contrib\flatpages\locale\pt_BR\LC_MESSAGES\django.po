# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> A<PERSON> <<EMAIL>>, 2014
# <PERSON> <amanda<PERSON><PERSON><PERSON><PERSON><EMAIL>>, 2019
# andrewsmed<PERSON> <<EMAIL>>, 2013
# dudanogue<PERSON> <<EMAIL>>, 2019
# <PERSON>, 2011
# <AUTHOR> <EMAIL>, 2013
# semente, 2012-2013
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-01-17 06:37+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) (http://www.transifex.com/django/django/"
"language/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Advanced options"
msgstr "Opções avançadas"

msgid "Flat Pages"
msgstr "Páginas Planas"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Exemplo: “/about/contact/”. Certifique-se de ter barras no início e no final."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Este valor deve conter apenas letras, números, pontos, sublinhados, traços, "
"barras ou til."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Exemplo: “/about/contact”. Certifique-se de ter uma barra no início."

msgid "URL is missing a leading slash."
msgstr "Está faltando uma barra no início da URL."

msgid "URL is missing a trailing slash."
msgstr "Está faltando uma barra no final da URL."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Uma flatpage com a URL %(url)s já existe para o site %(site)s"

msgid "title"
msgstr "título"

msgid "content"
msgstr "conteúdo"

msgid "enable comments"
msgstr "habilitar comentários"

msgid "template name"
msgstr "nome do template"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Exemplo: “flatpages/contact_page.html”. Se isso não for fornecido, o sistema "
"utilizará “flatpages/default.html”."

msgid "registration required"
msgstr "registro obrigatório"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Se estiver assinalado, apenas usuários autenticados poderão ver a página."

msgid "sites"
msgstr "sites"

msgid "flat page"
msgstr "página plana"

msgid "flat pages"
msgstr "páginas planas"
